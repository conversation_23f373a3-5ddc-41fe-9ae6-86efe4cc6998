{"backtrace": 7, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_scan_qml_imports", "_qt_internal_generate_deploy_qml_imports_script", "cmake_language", "_qt_internal_finalize_executable", "qt6_finalize_target"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake:787:EVAL", "CMakeLists.txt"], "nodes": [{"file": 3}, {"file": 3, "line": -1, "parent": 0}, {"command": 5, "file": 2, "line": 1, "parent": 1}, {"command": 4, "file": 1, "line": 818, "parent": 2}, {"command": 3, "file": 1, "line": 740, "parent": 3}, {"command": 2, "file": 1, "line": 740, "parent": 4}, {"command": 1, "file": 0, "line": 4401, "parent": 5}, {"command": 0, "file": 0, "line": 4174, "parent": 6}]}, "folder": {"name": "QtInternalTargets"}, "id": "qmltest_qmlimportscan::@6890427a1f51a3e7e1df", "name": "qmltest_qmlimportscan", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 7, "isGenerated": true, "path": "build/CMakeFiles/qmltest_qmlimportscan", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/qmltest_qmlimportscan.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/.qt/qml_imports/qmltest_build.cmake.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}