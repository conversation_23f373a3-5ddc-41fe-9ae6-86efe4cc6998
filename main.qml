import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15

ApplicationWindow {
    id: window
    width: 800
    height: 600
    visible: true
    title: "LAN Device Grid View"

    // 原始数据模型
    ListModel {
        id: lanDeviceModel
        ListElement { lanName: "LAN 1"; deviceName: "视讯01"; highlighted: true }
        ListElement { lanName: "LAN 1"; deviceName: "H3-01"; highlighted: false }
        ListElement { lanName: "LAN 1"; deviceName: "VST-01"; highlighted: false }
        ListElement { lanName: "LAN 2"; deviceName: "视讯02"; highlighted: false }
        ListElement { lanName: "LAN 2"; deviceName: "H3-02"; highlighted: false }
        ListElement { lanName: "LAN 2"; deviceName: "VST-02"; highlighted: true }
        ListElement { lanName: "LAN 3"; deviceName: "视讯03"; highlighted: true }
        ListElement { lanName: "LAN 3"; deviceName: "H3-03"; highlighted: false }
        ListElement { lanName: "LAN 3"; deviceName: "VST-03"; highlighted: false }
        ListElement { lanName: "LAN 4"; deviceName: "视讯04"; highlighted: false }
        ListElement { lanName: "LAN 4"; deviceName: "H3-04"; highlighted: false }
        ListElement { lanName: "LAN 4"; deviceName: "VST-04"; highlighted: true }
        ListElement { lanName: "LAN 5"; deviceName: "视讯05"; highlighted: false }
        ListElement { lanName: "LAN 5"; deviceName: "H3-05"; highlighted: false }
        ListElement { lanName: "LAN 5"; deviceName: "VST-05"; highlighted: true }
    }

    // 处理后的分组数据模型
    ListModel {
        id: groupedLanModel
    }

    // JavaScript函数：将原始数据按LAN分组
    function groupDataByLan() {
        groupedLanModel.clear()
        
        var lanGroups = {}
        
        // 按LAN名称分组
        for (var i = 0; i < lanDeviceModel.count; i++) {
            var item = lanDeviceModel.get(i)
            var lanName = item.lanName
            
            if (!lanGroups[lanName]) {
                lanGroups[lanName] = []
            }
            
            lanGroups[lanName].push({
                deviceName: item.deviceName,
                highlighted: item.highlighted
            })
        }
        
        // 将分组数据添加到新模型中
        for (var lan in lanGroups) {
            groupedLanModel.append({
                lanName: lan,
                devices: lanGroups[lan]
            })
        }
    }

    Component.onCompleted: {
        groupDataByLan()
    }

    Rectangle {
        anchors.fill: parent
        color: "#f0f0f0"
        
        GridView {
            id: gridView
            anchors.fill: parent
            anchors.margins: 20
            
            cellWidth: width / 2  // 2列布局
            cellHeight: 150       // 每个格子的高度
            
            model: groupedLanModel
            
            delegate: Rectangle {
                width: gridView.cellWidth - 10
                height: gridView.cellHeight - 10
                color: "white"
                border.color: "#cccccc"
                border.width: 1
                radius: 8
                
                Column {
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 8
                    
                    // LAN标题
                    Rectangle {
                        width: parent.width
                        height: 30
                        color: "#4a90e2"
                        radius: 4
                        
                        Text {
                            anchors.centerIn: parent
                            text: model.lanName
                            color: "white"
                            font.bold: true
                            font.pixelSize: 14
                        }
                    }
                    
                    // 设备列表
                    Column {
                        width: parent.width
                        spacing: 4
                        
                        Repeater {
                            model: devices
                            
                            Rectangle {
                                width: parent.width
                                height: 25
                                color: modelData.highlighted ? "#ffeb3b" : "#f5f5f5"
                                border.color: modelData.highlighted ? "#ffc107" : "#e0e0e0"
                                border.width: 1
                                radius: 3
                                
                                Text {
                                    anchors.centerIn: parent
                                    text: modelData.deviceName
                                    color: "#333333"
                                    font.pixelSize: 12
                                    font.bold: modelData.highlighted
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
