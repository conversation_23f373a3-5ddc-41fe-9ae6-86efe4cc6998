
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is Clang, found in:
        C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/4.0.2/CompilerIdCXX/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:1322 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:1322 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:250 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-e1wz7k"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-e1wz7k"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-e1wz7k'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_8c291
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_8c291.dir/CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_8c291.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles/cmTC_8c291.dir/CMakeCXXCompilerABI.cpp.obj -c F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -v -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_8c291.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_8c291.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_8c291.lib -Xlinker /pdb:cmTC_8c291.pdb -Xlinker /version:0.0     && cd ."
        clang version 19.1.1
        Target: x86_64-pc-windows-msvc
        Thread model: posix
        InstalledDir: C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin
         "C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\VC\\\\Tools\\\\Llvm\\\\x64\\\\bin\\\\lld-link" -out:cmTC_8c291.exe "-libpath:C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\VC\\\\Tools\\\\Llvm\\\\x64\\\\lib\\\\clang\\\\19\\\\lib\\\\windows" -nologo -debug /subsystem:console CMakeFiles/cmTC_8c291.dir/CMakeCXXCompilerABI.cpp.obj /MANIFEST:EMBED /implib:cmTC_8c291.lib /pdb:cmTC_8c291.pdb /version:0.0\x0d
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link" "-v"
      lld-link: warning: ignoring unknown argument '-v'
      lld-link: error: no input files
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link" "-V"
      lld-link: warning: ignoring unknown argument '-V'
      lld-link: error: no input files
  -
    kind: "message-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/lld-link" "--version"
      LLD 19.1.1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake:87 (_record_compiler_features)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake:124 (_record_compiler_features_cxx)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake:70 (cmake_record_cxx_compile_features)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:83 (CMAKE_DETERMINE_COMPILER_SUPPORT)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-d3ygrc"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-d3ygrc"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-d3ygrc'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_1f4e3
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd   -std=c++14 -MD -MT CMakeFiles/cmTC_1f4e3.dir/feature_tests.cxx.obj -MF CMakeFiles\\cmTC_1f4e3.dir\\feature_tests.cxx.obj.d -o CMakeFiles/cmTC_1f4e3.dir/feature_tests.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-d3ygrc/feature_tests.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_1f4e3.dir/feature_tests.cxx.obj -o cmTC_1f4e3.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_1f4e3.lib -Xlinker /pdb:cmTC_1f4e3.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake:87 (_record_compiler_features)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake:132 (_record_compiler_features_cxx)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake:70 (cmake_record_cxx_compile_features)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:83 (CMAKE_DETERMINE_COMPILER_SUPPORT)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-o5zxkv"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-o5zxkv"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-o5zxkv'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_0c0b4
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd   -std=c++14 -MD -MT CMakeFiles/cmTC_0c0b4.dir/feature_tests.cxx.obj -MF CMakeFiles\\cmTC_0c0b4.dir\\feature_tests.cxx.obj.d -o CMakeFiles/cmTC_0c0b4.dir/feature_tests.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-o5zxkv/feature_tests.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_0c0b4.dir/feature_tests.cxx.obj -o cmTC_0c0b4.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_0c0b4.lib -Xlinker /pdb:cmTC_0c0b4.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:99 (check_cxx_source_compiles)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_5666a
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -DCMAKE_HAVE_LIBC_PTHREAD  -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_5666a.dir/src.cxx.obj -MF CMakeFiles\\cmTC_5666a.dir\\src.cxx.obj.d -o CMakeFiles/cmTC_5666a.dir/src.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0/src.cxx
        FAILED: CMakeFiles/cmTC_5666a.dir/src.cxx.obj 
        "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -DCMAKE_HAVE_LIBC_PTHREAD  -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_5666a.dir/src.cxx.obj -MF CMakeFiles\\cmTC_5666a.dir\\src.cxx.obj.d -o CMakeFiles/cmTC_5666a.dir/src.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0/src.cxx
        C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-bkfcq0/src.cxx:1:10: fatal error: 'pthread.h' file not found\x0d
            1 | #include <pthread.h>\x0d
              |          ^~~~~~~~~~~\x0d
        1 error generated.\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:136 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:169 (_threads_check_flag_pthread)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Check if compiler accepts -pthread"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "THREADS_HAVE_PTHREAD_ARG"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_94641
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_94641.dir/CheckForPthreads.cxx.obj -MF CMakeFiles\\cmTC_94641.dir\\CheckForPthreads.cxx.obj.d -o CMakeFiles/cmTC_94641.dir/CheckForPthreads.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57/CheckForPthreads.cxx
        FAILED: CMakeFiles/cmTC_94641.dir/CheckForPthreads.cxx.obj 
        "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_94641.dir/CheckForPthreads.cxx.obj -MF CMakeFiles\\cmTC_94641.dir\\CheckForPthreads.cxx.obj.d -o CMakeFiles/cmTC_94641.dir/CheckForPthreads.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57/CheckForPthreads.cxx
        C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-rcbx57/CheckForPthreads.cxx:1:10: fatal error: 'pthread.h' file not found\x0d
            1 | #include <pthread.h>\x0d
              |          ^~~~~~~~~~~\x0d
        1 error generated.\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yr6f2b"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yr6f2b"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yr6f2b'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_27c88
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_27c88.dir/CheckFunctionExists.cxx.obj -MF CMakeFiles\\cmTC_27c88.dir\\CheckFunctionExists.cxx.obj.d -o CMakeFiles/cmTC_27c88.dir/CheckFunctionExists.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-yr6f2b/CheckFunctionExists.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_27c88.dir/CheckFunctionExists.cxx.obj -o cmTC_27c88.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_27c88.lib -Xlinker /pdb:cmTC_27c88.pdb -Xlinker /version:0.0   -lpthreads.lib  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        FAILED: cmTC_27c88.exe 
        C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_27c88.dir/CheckFunctionExists.cxx.obj -o cmTC_27c88.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_27c88.lib -Xlinker /pdb:cmTC_27c88.pdb -Xlinker /version:0.0   -lpthreads.lib  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        lld-link: error: could not open 'pthreads.lib': no such file or directory\x0d
        clang: error: linker command failed with exit code 1 (use -v to see invocation)\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-v9nzmi"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-v9nzmi"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-v9nzmi'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_eee8a
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe"   -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_eee8a.dir/CheckFunctionExists.cxx.obj -MF CMakeFiles\\cmTC_eee8a.dir\\CheckFunctionExists.cxx.obj.d -o CMakeFiles/cmTC_eee8a.dir/CheckFunctionExists.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-v9nzmi/CheckFunctionExists.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_eee8a.dir/CheckFunctionExists.cxx.obj -o cmTC_eee8a.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_eee8a.lib -Xlinker /pdb:cmTC_eee8a.pdb -Xlinker /version:0.0   -lpthread.lib  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        FAILED: cmTC_eee8a.exe 
        C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -DCHECK_FUNCTION_EXISTS=pthread_create -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_eee8a.dir/CheckFunctionExists.cxx.obj -o cmTC_eee8a.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_eee8a.lib -Xlinker /pdb:cmTC_eee8a.pdb -Xlinker /version:0.0   -lpthread.lib  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        lld-link: error: could not open 'pthread.lib': no such file or directory\x0d
        clang: error: linker command failed with exit code 1 (use -v to see invocation)\x0d
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake:78 (find_package)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/Qt6Config.cmake:218 (find_package)"
      - "CMakeLists.txt:8 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-pp2z1c"
      binary: "C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-pp2z1c"
    cmakeVariables:
      CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/Llvm/x64/bin/clang-scan-deps.exe"
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-pp2z1c'
        
        Run Build Command(s): "C:/PROGRA~1/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/Ninja/ninja.exe" -v cmTC_ffd6e
        [1/2] "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -DHAVE_STDATOMIC  -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_ffd6e.dir/src.cxx.obj -MF CMakeFiles\\cmTC_ffd6e.dir\\src.cxx.obj.d -o CMakeFiles/cmTC_ffd6e.dir/src.cxx.obj -c C:/Users/<USER>/Documents/augment-projects/qmltest/build/CMakeFiles/CMakeScratch/TryCompile-pp2z1c/src.cxx
        [2/2] C:\\Windows\\system32\\cmd.exe /C "cd . && "C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe" -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console -fuse-ld=lld-link CMakeFiles/cmTC_ffd6e.dir/src.cxx.obj -o cmTC_ffd6e.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_ffd6e.lib -Xlinker /pdb:cmTC_ffd6e.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
...
