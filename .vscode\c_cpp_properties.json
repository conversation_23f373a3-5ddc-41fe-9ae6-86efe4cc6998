{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "C:/Qt/6.9.1/mingw_64/include/**", "C:/Qt/6.9.1/mingw_64/include/QtCore/**", "C:/Qt/6.9.1/mingw_64/include/QtQuick/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "C:/Qt/Tools/mingw1310_64/bin/g++.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-gcc-x64"}], "version": 4}