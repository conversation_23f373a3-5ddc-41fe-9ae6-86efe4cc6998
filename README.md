# QML LAN Device Grid View

这个项目实现了一个使用GridView展示LAN设备的QML应用程序。

## 项目结构

- `simple_main.qml` - 简化版本，可以直接用qml工具运行
- `main.qml` - 完整版本，需要编译
- `main.cpp` - C++主文件
- `CMakeLists.txt` - CMake构建文件

## 功能特点

1. **GridView布局**: 使用2列网格布局显示LAN设备组
2. **数据分组**: 自动将ListModel中的数据按LAN名称分组
3. **高亮显示**: 支持设备的高亮显示功能
4. **响应式设计**: 自适应窗口大小

## 运行方式

### 方式1: 直接运行QML文件（推荐）
```bash
qml simple_main.qml
```

### 方式2: 编译运行
```bash
mkdir build
cd build
cmake ..
cmake --build .
./qmltest  # Linux/Mac
# 或
qmltest.exe  # Windows
```

## 代码说明

### 数据模型
原始的ListModel包含以下字段：
- `lanName`: LAN名称（如"LAN 1", "LAN 2"等）
- `deviceName`: 设备名称（如"视讯01", "H3-01"等）
- `highlighted`: 是否高亮显示

### 数据处理
使用JavaScript函数`groupDataByLan()`将原始数据按LAN名称分组，生成新的模型用于GridView显示。

### 界面布局
- GridView使用2列布局（cellWidth: width / 2）
- 每个格子包含LAN标题和设备列表
- 高亮的设备使用黄色背景显示

## 效果展示

最终效果将显示为：
```
[ LAN 1 ]      [ LAN 2 ]
视讯01         视讯02
H3-01          H3-02
VST-01         VST-02

[ LAN 3 ]      [ LAN 4 ]
视讯03         视讯04
H3-03          H3-04
VST-03         VST-04

[ LAN 5 ]
视讯05
H3-05
VST-05
```

其中高亮的设备会以黄色背景显示。
