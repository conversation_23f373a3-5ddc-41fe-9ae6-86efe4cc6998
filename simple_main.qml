import QtQuick 2.15

Rectangle {
    width: 800
    height: 600
    color: "#f0f0f0"

    // 你的原始数据模型
    ListModel {
        id: lanDeviceModel
        ListElement { lanName: "LAN 1"; deviceName: "视讯01"; highlighted: true }
        ListElement { lanName: "LAN 1"; deviceName: "H3-01"; highlighted: false }
        ListElement { lanName: "LAN 1"; deviceName: "VST-01"; highlighted: false }
        ListElement { lanName: "LAN 2"; deviceName: "视讯02"; highlighted: false }
        ListElement { lanName: "LAN 2"; deviceName: "H3-02"; highlighted: false }
        ListElement { lanName: "LAN 2"; deviceName: "VST-02"; highlighted: true }
        ListElement { lanName: "LAN 3"; deviceName: "视讯03"; highlighted: true }
        ListElement { lanName: "LAN 3"; deviceName: "H3-03"; highlighted: false }
        ListElement { lanName: "LAN 3"; deviceName: "VST-03"; highlighted: false }
        ListElement { lanName: "LAN 4"; deviceName: "视讯04"; highlighted: false }
        ListElement { lanName: "LAN 4"; deviceName: "H3-04"; highlighted: false }
        ListElement { lanName: "LAN 4"; deviceName: "VST-04"; highlighted: true }
        ListElement { lanName: "LAN 5"; deviceName: "视讯05"; highlighted: false }
        ListElement { lanName: "LAN 5"; deviceName: "H3-05"; highlighted: false }
        ListElement { lanName: "LAN 5"; deviceName: "VST-05"; highlighted: true }
    }

    // 分组后的数据模型
    ListModel {
        id: groupedLanModel
    }

    // 数据分组函数
    function groupDataByLan() {
        groupedLanModel.clear()
        var lanGroups = {}

        // 按LAN名称分组
        for (var i = 0; i < lanDeviceModel.count; i++) {
            var item = lanDeviceModel.get(i)
            var lanName = item.lanName

            if (!lanGroups[lanName]) {
                lanGroups[lanName] = {
                    lanName: lanName,
                    deviceList: []
                }
            }

            lanGroups[lanName].deviceList.push({
                deviceName: item.deviceName,
                highlighted: item.highlighted
            })
        }

        // 将分组数据添加到新模型
        for (var lan in lanGroups) {
            var group = lanGroups[lan]
            for (var j = 0; j < group.deviceList.length; j++) {
                groupedLanModel.append({
                    lanName: group.lanName,
                    deviceName: group.deviceList[j].deviceName,
                    highlighted: group.deviceList[j].highlighted,
                    isFirstInGroup: j === 0
                })
            }
        }
    }

    Component.onCompleted: {
        groupDataByLan()
    }

    // 创建分组后的显示模型
    ListModel {
        id: displayModel
    }

    function createDisplayModel() {
        displayModel.clear()
        var lanGroups = {}

        // 按LAN分组收集设备
        for (var i = 0; i < lanDeviceModel.count; i++) {
            var item = lanDeviceModel.get(i)
            var lanName = item.lanName

            if (!lanGroups[lanName]) {
                lanGroups[lanName] = []
            }

            lanGroups[lanName].push({
                deviceName: item.deviceName,
                highlighted: item.highlighted
            })
        }

        // 为每个LAN组创建显示项
        for (var lan in lanGroups) {
            displayModel.append({
                lanName: lan,
                deviceList: lanGroups[lan]
            })
        }
    }

    Component.onCompleted: {
        createDisplayModel()
    }

    GridView {
        id: gridView
        anchors.fill: parent
        anchors.margins: 20

        cellWidth: width / 2  // 2列布局
        cellHeight: 150       // 每个格子高度

        model: displayModel

        delegate: Rectangle {
            width: gridView.cellWidth - 10
            height: gridView.cellHeight - 10
            color: "white"
            border.color: "#cccccc"
            border.width: 1
            radius: 8

            Column {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 5

                // LAN标题
                Rectangle {
                    width: parent.width
                    height: 25
                    color: "#4a90e2"
                    radius: 4

                    Text {
                        anchors.centerIn: parent
                        text: "[ " + model.lanName + " ]"
                        color: "white"
                        font.bold: true
                        font.pixelSize: 12
                    }
                }

                // 设备列表
                Column {
                    width: parent.width
                    spacing: 2

                    Repeater {
                        model: deviceList

                        Text {
                            width: parent.width
                            height: 20
                            text: modelData.deviceName
                            color: modelData.highlighted ? "#ff6600" : "#333333"
                            font.pixelSize: 11
                            font.bold: modelData.highlighted
                            horizontalAlignment: Text.AlignHCenter
                            verticalAlignment: Text.AlignVCenter
                        }
                    }
                }
            }
        }
    }
}
