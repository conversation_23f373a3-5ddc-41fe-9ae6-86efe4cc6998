import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15

Window {
    id: window
    width: 800
    height: 600
    visible: true
    title: "LAN Device Grid View"

    ListModel {
        id: groupedLanModel
        ListElement {
            lanName: "LAN 1"
            devices: [
                ListElement { deviceName: "视讯01"; highlighted: true },
                ListElement { deviceName: "H3-01"; highlighted: false },
                ListElement { deviceName: "VST-01"; highlighted: false }
            ]
        }
        ListElement {
            lanName: "LAN 2"
            devices: [
                ListElement { deviceName: "视讯02"; highlighted: true },
                ListElement { deviceName: "H3-02"; highlighted: false },
                ListElement { deviceName: "VST-02"; highlighted: false }
            ]
        }
        ListElement {
            lanName: "LAN 3"
            devices: [
                ListElement { deviceName: "视讯03"; highlighted: true },
                ListElement { deviceName: "H3-03"; highlighted: false },
                ListElement { deviceName: "VST-03"; highlighted: false }
            ]
        }
        ListElement {
            lanName: "LAN 4"
            devices: [
                ListElement { deviceName: "视讯04"; highlighted: true },
                ListElement { deviceName: "H3-04"; highlighted: false },
                ListElement { deviceName: "VST-04"; highlighted: false }
            ]
        }
        ListElement {
            lanName: "LAN 5"
            devices: [
                ListElement { deviceName: "视讯05"; highlighted: true },
                ListElement { deviceName: "H3-05"; highlighted: false },
                ListElement { deviceName: "VST-05"; highlighted: false }
            ]
        }
    }

    Rectangle {
        anchors.fill: parent
        color: "#f0f0f0"
        
        GridView {
            id: gridView
            anchors.fill: parent
            anchors.margins: 20
            
            cellWidth: width / 2  // 2列布局
            cellHeight: 150       // 每个格子的高度
            
            model: groupedLanModel
            
            delegate: Rectangle {
                width: gridView.cellWidth - 10
                height: gridView.cellHeight - 10
                color: "white"
                border.color: "#cccccc"
                border.width: 1
                radius: 8
                
                Column {
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 8
                    
                    // LAN标题
                    Rectangle {
                        width: parent.width
                        height: 30
                        color: "#4a90e2"
                        radius: 4
                        
                        Text {
                            anchors.centerIn: parent
                            text: model.lanName
                            color: "white"
                            font.bold: true
                            font.pixelSize: 14
                        }
                    }
                    
                    // 设备列表 - 使用ListView
                    ListView {
                        width: parent.width
                        height: parent.height - 38  // 减去标题高度和间距
                        spacing: 4
                        interactive: false  // 禁用滚动

                        model: devices

                        delegate: Rectangle {
                            width: parent.width
                            height: 25
                            color: modelData.highlighted ? "#ffeb3b" : "#f5f5f5"
                            border.color: modelData.highlighted ? "#ffc107" : "#e0e0e0"
                            border.width: 1
                            radius: 3

                            Text {
                                anchors.centerIn: parent
                                text: modelData.deviceName
                                color: "#333333"
                                font.pixelSize: 12
                                font.bold: modelData.highlighted
                            }
                        }
                    }
                }
            }
        }
    }
}
