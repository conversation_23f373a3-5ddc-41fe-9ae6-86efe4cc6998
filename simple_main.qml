import QtQuick 2.15

Rectangle {
    width: 800
    height: 600
    color: "#f0f0f0"

    // LAN 1 设备模型
    ListModel {
        id: lan1Model
        ListElement { deviceName: "视讯01"; highlighted: true }
        ListElement { deviceName: "H3-01"; highlighted: false }
        ListElement { deviceName: "VST-01"; highlighted: false }
    }

    // LAN 2 设备模型
    ListModel {
        id: lan2Model
        ListElement { deviceName: "视讯02"; highlighted: false }
        ListElement { deviceName: "H3-02"; highlighted: false }
        ListElement { deviceName: "VST-02"; highlighted: true }
    }

    // LAN 3 设备模型
    ListModel {
        id: lan3Model
        ListElement { deviceName: "视讯03"; highlighted: true }
        ListElement { deviceName: "H3-03"; highlighted: false }
        ListElement { deviceName: "VST-03"; highlighted: false }
    }

    // LAN 4 设备模型
    ListModel {
        id: lan4Model
        ListElement { deviceName: "视讯04"; highlighted: false }
        ListElement { deviceName: "H3-04"; highlighted: false }
        ListElement { deviceName: "VST-04"; highlighted: true }
    }

    // LAN 5 设备模型
    ListModel {
        id: lan5Model
        ListElement { deviceName: "视讯05"; highlighted: false }
        ListElement { deviceName: "H3-05"; highlighted: false }
        ListElement { deviceName: "VST-05"; highlighted: true }
    }

    // 主显示模型
    ListModel {
        id: displayModel
        ListElement { lanName: "LAN 1" }
        ListElement { lanName: "LAN 2" }
        ListElement { lanName: "LAN 3" }
        ListElement { lanName: "LAN 4" }
        ListElement { lanName: "LAN 5" }
    }

    // 根据LAN名称获取对应的设备模型
    function getDeviceModel(lanName) {
        switch(lanName) {
            case "LAN 1": return lan1Model
            case "LAN 2": return lan2Model
            case "LAN 3": return lan3Model
            case "LAN 4": return lan4Model
            case "LAN 5": return lan5Model
            default: return null
        }
    }

    GridView {
        id: gridView
        anchors.fill: parent
        anchors.margins: 20

        cellWidth: width / 2  // 2列布局
        cellHeight: 150       // 每个格子高度

        model: displayModel

        delegate: Rectangle {
            width: gridView.cellWidth - 10
            height: gridView.cellHeight - 10
            color: "white"
            border.color: "#cccccc"
            border.width: 1
            radius: 8

            property string currentLanName: model.lanName

            Column {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 5

                // LAN标题
                Rectangle {
                    width: parent.width
                    height: 25
                    color: "#4a90e2"
                    radius: 4

                    Text {
                        anchors.centerIn: parent
                        text: "[ " + currentLanName + " ]"
                        color: "white"
                        font.bold: true
                        font.pixelSize: 12
                    }
                }

                // 设备列表 - 使用ListView
                ListView {
                    width: parent.width
                    height: parent.height - 30  // 减去标题高度
                    spacing: 2
                    interactive: false  // 禁用滚动

                    model: getDeviceModel(currentLanName)

                    delegate: Text {
                        width: parent.width
                        height: 20
                        text: model.deviceName
                        color: model.highlighted ? "#ff6600" : "#333333"
                        font.pixelSize: 11
                        font.bold: model.highlighted
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
    }
}
