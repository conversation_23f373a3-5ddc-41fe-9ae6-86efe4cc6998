import QtQuick 2.15

Rectangle {
    width: 800
    height: 600
    color: "#f0f0f0"

    // 直接定义分组数据
    property var lanData: [
        {
            lanName: "LAN 1",
            deviceList: [
                { deviceName: "视讯01", highlighted: true },
                { deviceName: "H3-01", highlighted: false },
                { deviceName: "VST-01", highlighted: false }
            ]
        },
        {
            lanName: "LAN 2",
            deviceList: [
                { deviceName: "视讯02", highlighted: false },
                { deviceName: "H3-02", highlighted: false },
                { deviceName: "VST-02", highlighted: true }
            ]
        },
        {
            lanName: "LAN 3",
            deviceList: [
                { deviceName: "视讯03", highlighted: true },
                { deviceName: "H3-03", highlighted: false },
                { deviceName: "VST-03", highlighted: false }
            ]
        },
        {
            lanName: "LAN 4",
            deviceList: [
                { deviceName: "视讯04", highlighted: false },
                { deviceName: "H3-04", highlighted: false },
                { deviceName: "VST-04", highlighted: true }
            ]
        },
        {
            lanName: "LAN 5",
            deviceList: [
                { deviceName: "视讯05", highlighted: false },
                { deviceName: "H3-05", highlighted: false },
                { deviceName: "VST-05", highlighted: true }
            ]
        }
    ]

    GridView {
        id: gridView
        anchors.fill: parent
        anchors.margins: 20

        cellWidth: width / 2  // 2列布局
        cellHeight: 150       // 每个格子高度

        model: lanData

        delegate: Rectangle {
            width: gridView.cellWidth - 10
            height: gridView.cellHeight - 10
            color: "white"
            border.color: "#cccccc"
            border.width: 1
            radius: 8

            Column {
                anchors.fill: parent
                anchors.margins: 10
                spacing: 5

                // LAN标题
                Rectangle {
                    width: parent.width
                    height: 25
                    color: "#4a90e2"
                    radius: 4

                    Text {
                        anchors.centerIn: parent
                        text: "[ " + model.lanName + " ]"
                        color: "white"
                        font.bold: true
                        font.pixelSize: 12
                    }
                }

                // 设备列表 - 使用ListView
                ListView {
                    width: parent.width
                    height: parent.height - 30  // 减去标题高度
                    spacing: 2
                    interactive: false  // 禁用滚动

                    model: modelData.deviceList

                    delegate: Text {
                        width: parent.width
                        height: 20
                        text: modelData.deviceName
                        color: modelData.highlighted ? "#ff6600" : "#333333"
                        font.pixelSize: 11
                        font.bold: modelData.highlighted
                        horizontalAlignment: Text.AlignHCenter
                        verticalAlignment: Text.AlignVCenter
                    }
                }
            }
        }
    }
}
