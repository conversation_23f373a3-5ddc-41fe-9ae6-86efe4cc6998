/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.1
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // main.qml
  0x0,0x0,0x4,0x8b,
  0x0,
  0x0,0x13,0xee,0x78,0xda,0xad,0x57,0x4d,0x6f,0x1b,0x45,0x18,0xbe,0xfb,0x57,0xc,
  0xe6,0xe2,0xa8,0xed,0x66,0xbd,0xb6,0xa1,0xb8,0x20,0x54,0x52,0xd4,0x52,0x85,0x4a,
  0x6d,0xaa,0xf6,0x50,0xe5,0x30,0xde,0x1d,0xdb,0x23,0xc6,0xbb,0xab,0xf5,0x38,0x2e,
  0x44,0x96,0x2a,0x11,0x25,0x81,0xd2,0x92,0xb,0x50,0xf5,0x0,0x29,0xaa,0x48,0x5,
  0x6a,0x6a,0x4e,0x44,0xb4,0xf0,0x6b,0xba,0x4e,0x7a,0xe2,0x2f,0x30,0x33,0xfb,0xe1,
  0xfd,0x9a,0xf5,0x36,0xf6,0xec,0xc1,0xbb,0xf3,0x3e,0xf3,0xce,0xfb,0xbc,0x5f,0x33,
  0xc6,0x3d,0xdb,0x72,0x28,0xb8,0x4e,0xaf,0xf,0xb0,0xfe,0x5,0xd0,0x94,0x6a,0xa3,
  0x84,0x63,0x73,0xca,0x8a,0x65,0x52,0xc7,0x22,0xfd,0x4c,0xe1,0x6d,0x6c,0x1a,0xd6,
  0xd0,0x13,0x95,0x2e,0xda,0x36,0xc1,0x3a,0xa4,0xd8,0x32,0xfd,0xf9,0xcd,0x12,0x60,
  0x3,0x1b,0x4d,0x30,0x14,0x13,0xe2,0x73,0x88,0xd,0xda,0x6d,0x82,0xf3,0xaa,0x2a,
  0x3e,0xbb,0x8,0x77,0xba,0xb4,0x9,0xde,0xf3,0xbf,0x37,0x70,0x1f,0xb7,0x8,0x6a,
  0x2,0xea,0xc,0x90,0x98,0xa1,0x98,0xf2,0xef,0xf2,0xea,0xc5,0x6b,0xe0,0x12,0xda,
  0xc0,0x3a,0x2,0x97,0x1d,0x6c,0x80,0x5b,0x18,0xd,0xcb,0x25,0x1,0x59,0x5e,0x6,
  0xee,0xc3,0x5f,0xdc,0x83,0xfb,0x93,0x1f,0xc6,0x93,0x7,0x87,0x93,0x67,0x4f,0xdc,
  0x9f,0xef,0xb,0xc9,0x2a,0xee,0xd3,0xcf,0x2d,0x3,0x11,0xdf,0x98,0xc0,0x20,0x2,
  0x4d,0x4f,0x97,0x10,0x86,0x22,0xe,0xff,0x94,0xa0,0x1e,0x32,0x29,0xd8,0xe4,0xa0,
  0x6b,0xb0,0x17,0xec,0x5d,0x2d,0x5f,0x0,0x86,0x58,0xe3,0x4f,0x9e,0x1c,0x6c,0x9f,
  0x1c,0xbe,0x50,0xf9,0x7c,0x97,0x91,0x20,0x9c,0x8,0x32,0x3c,0xcb,0xc1,0xe8,0x74,
  0x3a,0xaf,0xd4,0xce,0xa5,0x15,0xb6,0x21,0xe9,0x9f,0x5a,0xe3,0xad,0xb5,0x9b,0x73,
  0xaa,0xd4,0x24,0xc4,0xb5,0x85,0x2a,0xe5,0xcc,0x17,0xab,0x51,0x30,0xd7,0xe6,0x88,
  0x4e,0x4d,0x42,0xbc,0xb6,0x48,0x9d,0x9c,0x77,0x6d,0x1e,0xde,0xb5,0x4c,0xde,0x73,
  0xa9,0xac,0x4b,0x88,0xd7,0x17,0xaa,0x94,0x33,0x5f,0xac,0x46,0xc1,0xbc,0x3e,0x47,
  0x74,0x1a,0x12,0xe2,0x8d,0x79,0xcc,0x6c,0x64,0x11,0x5f,0xac,0x46,0x41,0xbc,0x91,
  0x43,0x7c,0x34,0x6d,0x94,0x4f,0xb7,0x8e,0xf7,0xb6,0xdd,0xbd,0x87,0xc7,0x8f,0xb7,
  0xdc,0xdd,0xed,0xe3,0x97,0x5b,0x85,0x9b,0x66,0xc7,0xb1,0x6,0x36,0x32,0x56,0xa1,
  0x39,0xed,0x9a,0x53,0xc5,0x57,0xe1,0x6,0x5c,0xd3,0x1d,0x6c,0x53,0x77,0xe7,0x1f,
  0xa6,0xf3,0xbf,0x57,0x8f,0xdd,0xf1,0x76,0xac,0x2f,0x7f,0xf7,0xd,0xb3,0xde,0xdb,
  0x54,0xac,0x6a,0xf,0x4c,0x9d,0x9f,0x17,0x9e,0xe6,0x4b,0x90,0xc2,0x4f,0xbe,0x64,
  0xda,0x2b,0x4b,0x91,0x8d,0x13,0x9b,0x2a,0x3a,0x41,0xd0,0xa9,0x2c,0x85,0xf2,0xf0,
  0x65,0x3,0x3a,0xdc,0x4d,0x97,0x39,0xbe,0xf,0x3e,0x2,0x9b,0xa3,0x34,0x86,0x99,
  0xe9,0x1b,0xb1,0xf7,0xe0,0xf8,0x60,0x1c,0x31,0x45,0x98,0x63,0x39,0xa0,0xc2,0xd5,
  0x60,0xb6,0x5c,0xbd,0xc0,0x7e,0x3e,0x4c,0x1c,0x14,0x8a,0x6e,0xd,0x4c,0xca,0x24,
  0x67,0xce,0x44,0x6d,0xc,0xb6,0xc7,0x14,0xf5,0xd8,0xd2,0xc4,0x9a,0xe,0xa2,0x15,
  0xbc,0x94,0x2,0xfb,0x21,0x65,0x78,0xbe,0x4c,0xf1,0x3f,0x63,0xb0,0xd8,0x7,0x6e,
  0x83,0xca,0x3b,0x21,0xc1,0x3b,0x3e,0x7e,0x3d,0x69,0x7,0x1f,0x69,0x14,0xdb,0xe5,
  0xce,0x7a,0xc,0x37,0x92,0xef,0x94,0x5e,0xae,0xd8,0x83,0x7e,0xb7,0x92,0xde,0x28,
  0x9a,0x84,0x82,0xc5,0x74,0xe2,0x6c,0xa,0x1c,0xcb,0x4d,0x81,0x8e,0xcc,0xc4,0x6d,
  0x9b,0x7a,0x2b,0x3b,0x88,0x3c,0xb1,0xa2,0xb9,0xfb,0xd7,0x4b,0xf7,0xdb,0x7d,0x77,
  0x77,0x3c,0xf9,0x71,0xec,0xe5,0xf1,0xeb,0xa3,0xe7,0xe9,0xb0,0x32,0x36,0x0,0x9b,
  0x53,0x7a,0x49,0xd7,0x25,0x53,0xd,0xda,0x36,0x32,0x8d,0x4a,0xa6,0x7f,0x3d,0xce,
  0xec,0xe5,0xac,0xc4,0x29,0xfd,0x66,0xdc,0x8f,0xeb,0xf9,0xc,0xfd,0x3a,0x5a,0xb1,
  0xd8,0xd,0xcb,0x64,0x35,0xaf,0x58,0x26,0x7f,0x27,0x48,0x78,0x2b,0x51,0xe,0x91,
  0x4a,0x89,0x2e,0xbe,0x81,0x74,0xa,0xcd,0xe,0x41,0x11,0x3c,0x34,0xf5,0xae,0xe5,
  0xf4,0x95,0x36,0x26,0xa4,0x9,0x6c,0xe8,0x30,0xdd,0xa1,0x50,0xb7,0x88,0xe5,0xb0,
  0xf6,0xf1,0x6e,0x5b,0xe5,0x4f,0x39,0xed,0x6b,0x7e,0xd3,0xe2,0x17,0xad,0x84,0xa7,
  0xbc,0x6e,0xe0,0x89,0x62,0x82,0xdc,0xed,0xa2,0x80,0x1e,0x74,0x3a,0xd8,0x64,0x4e,
  0xd2,0x54,0x79,0x22,0xea,0x88,0x90,0xdb,0xde,0x7d,0x51,0x5c,0x1b,0xc1,0x32,0xd0,
  0x44,0xfc,0x35,0x77,0xf7,0x27,0xf7,0xe8,0x6b,0xf7,0xcf,0x7b,0x29,0xfc,0x15,0xff,
  0x42,0x59,0x6d,0xa8,0x91,0xa2,0x7f,0xf1,0xfd,0xeb,0xa3,0xdf,0x27,0xfb,0xaf,0xdc,
  0xe7,0x7b,0xac,0xef,0xbd,0xf9,0xe3,0x91,0xfb,0xf7,0x6f,0xf2,0x7d,0x7b,0x3c,0xfc,
  0xd9,0xfd,0x2e,0x13,0xcf,0x84,0xa8,0x3,0x29,0x4b,0x88,0xac,0x10,0x4,0xc3,0xbf,
  0xf9,0x6,0x7e,0x53,0x42,0x76,0xe0,0x1c,0xa8,0xaa,0xe9,0x72,0xf1,0x89,0xc4,0xf0,
  0x1e,0xbb,0xec,0x5,0x41,0x34,0x87,0x5d,0x56,0x5c,0xe5,0x94,0xb8,0x65,0x39,0x6,
  0x72,0x94,0x30,0xe6,0xba,0x18,0x52,0x9c,0x6f,0x6d,0x35,0x25,0x77,0xa0,0x81,0x7,
  0x2c,0x70,0xe7,0x53,0x92,0xd4,0xc4,0x8a,0x45,0x6,0x3d,0x33,0xc3,0x15,0x85,0x52,
  0x45,0x9a,0x32,0x19,0xdc,0xf9,0xe8,0xdb,0x50,0xc7,0x66,0x27,0xcb,0xb2,0x4c,0xeb,
  0xfc,0xdc,0x60,0xa7,0xc1,0x64,0x7f,0xe7,0xcd,0xaf,0x8f,0x32,0x1,0x79,0x1,0x4d,
  0x4,0xd6,0xa3,0xe0,0x39,0x4e,0x8a,0xd,0xa2,0x5a,0x53,0xa5,0x90,0x30,0x42,0x75,
  0xf8,0x81,0x8a,0xb4,0xb2,0x14,0x18,0x44,0xa2,0x2e,0x45,0x48,0x5,0x37,0xd1,0x5d,
  0x9a,0xc3,0x28,0xea,0x76,0x9d,0x91,0x42,0xce,0x67,0x66,0x6e,0x8c,0x82,0x41,0x99,
  0xde,0xa6,0x57,0x3f,0x99,0x27,0xda,0x5b,0xe6,0x6c,0x74,0xb4,0xd9,0x3f,0x51,0xa5,
  0x65,0x11,0x23,0xf2,0xdf,0x30,0x17,0x6b,0xe3,0xbb,0x88,0xac,0xe1,0xaf,0x58,0x59,
  0x56,0xe5,0x2e,0x1a,0x95,0x8a,0xcf,0xca,0x52,0xe8,0xe4,0xf0,0x5f,0xf7,0xe9,0xe,
  0xeb,0x49,0x27,0x4f,0x9e,0x65,0x62,0x72,0xb,0xe1,0x6d,0x53,0x28,0xcc,0xf3,0x53,
  0xc4,0xfd,0x6,0xb2,0x11,0x6b,0x54,0xce,0x8c,0xd8,0xfb,0xd,0xd0,0x3f,0xc9,0x72,
  0xa1,0xb9,0xc2,0x22,0xd5,0x73,0x1a,0x17,0x24,0xab,0x49,0x6b,0xcc,0x84,0xfa,0x99,
  0x26,0x98,0xf1,0xf3,0x33,0x7a,0xf9,0x0,0x1f,0xf3,0x33,0xb0,0x8d,0x5a,0xb5,0x56,
  0x19,0x88,0xf3,0xb0,0xc1,0x9f,0xf2,0x4c,0xa5,0xf1,0x9e,0x9a,0xa7,0x5b,0xaf,0xaa,
  0xef,0x7b,0xba,0x91,0xca,0x9f,0xc2,0xba,0xa5,0x7d,0x58,0xd6,0xd,0x6a,0x33,0x91,
  0x33,0x1,0x5,0xba,0xc3,0x5c,0x5d,0x22,0xa3,0x5b,0x8,0xb7,0x4d,0x6f,0x8f,0x85,
  0x96,0x87,0x8d,0xb2,0x26,0x46,0xb9,0xd0,0xa2,0x54,0x6b,0xd0,0x8a,0x2f,0xf3,0xba,
  0x4f,0x66,0x98,0x67,0x2a,0x19,0x95,0x4e,0x27,0x2d,0xda,0x9f,0x46,0x92,0xdb,0x7d,
  0x70,0xbd,0x1c,0x95,0xfe,0x7,0x4c,0xe3,0x34,0x90,
  
};

static const unsigned char qt_resource_name[] = {
  // qmltest
  0x0,0x7,
  0x8,0x43,0xac,0x44,
  0x0,0x71,
  0x0,0x6d,0x0,0x6c,0x0,0x74,0x0,0x65,0x0,0x73,0x0,0x74,
    // main.qml
  0x0,0x8,
  0x8,0x1,0x5a,0x5c,
  0x0,0x6d,
  0x0,0x61,0x0,0x69,0x0,0x6e,0x0,0x2e,0x0,0x71,0x0,0x6d,0x0,0x6c,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/qmltest
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/qmltest/main.qml
  0x0,0x0,0x0,0x14,0x0,0x1,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xb5,0xa3,0xf1,0x4c,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#if defined(__ELF__) || defined(__APPLE__)
static inline unsigned char qResourceFeatureZlib()
{
    extern const unsigned char qt_resourceFeatureZlib;
    return qt_resourceFeatureZlib;
}
#else
unsigned char qResourceFeatureZlib();
#endif

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_qmltest_raw_qml_0)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_qmltest_raw_qml_0)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qmltest_raw_qml_0)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qmltest_raw_qml_0)()
{
    int version = 3;
    version += QT_RCC_PREPEND_NAMESPACE(qResourceFeatureZlib());
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_qmltest_raw_qml_0)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_qmltest_raw_qml_0)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
