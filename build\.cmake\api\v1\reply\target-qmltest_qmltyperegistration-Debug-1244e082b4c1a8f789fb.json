{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_qml_type_registration", "qt6_add_qml_module", "qt_add_qml_module"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 16, "parent": 0}, {"command": 2, "file": 0, "line": 1252, "parent": 1}, {"command": 1, "file": 0, "line": 796, "parent": 2}, {"command": 0, "file": 0, "line": 3840, "parent": 3}]}, "folder": {"name": "QtInternalTargets"}, "id": "qmltest_qmltyperegistration::@6890427a1f51a3e7e1df", "name": "qmltest_qmltyperegistration", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 4, "isGenerated": true, "path": "build/CMakeFiles/qmltest_qmltyperegistration", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/qmltest_qmltyperegistration.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/qmltest_qmltyperegistrations.cpp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}