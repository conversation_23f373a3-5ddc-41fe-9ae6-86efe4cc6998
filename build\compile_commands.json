[{"directory": "C:/Users/<USER>/Documents/augment-projects/qmltest/build", "command": "\"C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe\" -DMINGW_HAS_SECURE_API=1 -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -IC:/Users/<USER>/Documents/augment-projects/qmltest/build/qmltest_autogen/include -IC:/Users/<USER>/Documents/augment-projects/qmltest -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtQuick -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlMeta -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlModels -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -isystem C:/Qt/6.9.1/mingw_64/include/QtOpenGL -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -o CMakeFiles\\qmltest.dir\\qmltest_autogen\\mocs_compilation.cpp.obj -c C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\qmltest_autogen\\mocs_compilation.cpp", "file": "C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\qmltest_autogen\\mocs_compilation.cpp", "output": "CMakeFiles\\qmltest.dir\\qmltest_autogen\\mocs_compilation.cpp.obj"}, {"directory": "C:/Users/<USER>/Documents/augment-projects/qmltest/build", "command": "\"C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe\" -DMINGW_HAS_SECURE_API=1 -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -IC:/Users/<USER>/Documents/augment-projects/qmltest/build/qmltest_autogen/include -IC:/Users/<USER>/Documents/augment-projects/qmltest -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtQuick -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlMeta -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlModels -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -isystem C:/Qt/6.9.1/mingw_64/include/QtOpenGL -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -o CMakeFiles\\qmltest.dir\\main.cpp.obj -c C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\main.cpp", "file": "C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\main.cpp", "output": "CMakeFiles\\qmltest.dir\\main.cpp.obj"}, {"directory": "C:/Users/<USER>/Documents/augment-projects/qmltest/build", "command": "\"C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe\" -DMINGW_HAS_SECURE_API=1 -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -IC:/Users/<USER>/Documents/augment-projects/qmltest/build/qmltest_autogen/include -IC:/Users/<USER>/Documents/augment-projects/qmltest -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtQuick -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlMeta -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlModels -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -isystem C:/Qt/6.9.1/mingw_64/include/QtOpenGL -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -o CMakeFiles\\qmltest.dir\\qmltest_qmltyperegistrations.cpp.obj -c C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\qmltest_qmltyperegistrations.cpp", "file": "C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\qmltest_qmltyperegistrations.cpp", "output": "CMakeFiles\\qmltest.dir\\qmltest_qmltyperegistrations.cpp.obj"}, {"directory": "C:/Users/<USER>/Documents/augment-projects/qmltest/build", "command": "\"C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe\" -DMINGW_HAS_SECURE_API=1 -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -IC:/Users/<USER>/Documents/augment-projects/qmltest/build/qmltest_autogen/include -IC:/Users/<USER>/Documents/augment-projects/qmltest -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtQuick -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlMeta -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlModels -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -isystem C:/Qt/6.9.1/mingw_64/include/QtOpenGL -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -o CMakeFiles\\qmltest.dir\\build\\.qt\\rcc\\qrc_qmake_qmltest.cpp.obj -c C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\.qt\\rcc\\qrc_qmake_qmltest.cpp", "file": "C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\.qt\\rcc\\qrc_qmake_qmltest.cpp", "output": "CMakeFiles\\qmltest.dir\\build\\.qt\\rcc\\qrc_qmake_qmltest.cpp.obj"}, {"directory": "C:/Users/<USER>/Documents/augment-projects/qmltest/build", "command": "\"C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe\" -DMINGW_HAS_SECURE_API=1 -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -IC:/Users/<USER>/Documents/augment-projects/qmltest/build/qmltest_autogen/include -IC:/Users/<USER>/Documents/augment-projects/qmltest -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtQuick -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlMeta -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlModels -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -isystem C:/Qt/6.9.1/mingw_64/include/QtOpenGL -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -o CMakeFiles\\qmltest.dir\\build\\.rcc\\qmlcache\\qmltest_qmlcache_loader.cpp.obj -c C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\.rcc\\qmlcache\\qmltest_qmlcache_loader.cpp", "file": "C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\.rcc\\qmlcache\\qmltest_qmlcache_loader.cpp", "output": "CMakeFiles\\qmltest.dir\\build\\.rcc\\qmlcache\\qmltest_qmlcache_loader.cpp.obj"}, {"directory": "C:/Users/<USER>/Documents/augment-projects/qmltest/build", "command": "\"C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe\" -DMINGW_HAS_SECURE_API=1 -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -IC:/Users/<USER>/Documents/augment-projects/qmltest/build/qmltest_autogen/include -IC:/Users/<USER>/Documents/augment-projects/qmltest -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtQuick -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlMeta -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlModels -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -isystem C:/Qt/6.9.1/mingw_64/include/QtOpenGL -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -o CMakeFiles\\qmltest.dir\\build\\.rcc\\qmlcache\\qmltest_main_qml.cpp.obj -c C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\.rcc\\qmlcache\\qmltest_main_qml.cpp", "file": "C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\.rcc\\qmlcache\\qmltest_main_qml.cpp", "output": "CMakeFiles\\qmltest.dir\\build\\.rcc\\qmlcache\\qmltest_main_qml.cpp.obj"}, {"directory": "C:/Users/<USER>/Documents/augment-projects/qmltest/build", "command": "\"C:\\PROGRA~1\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe\" -DMINGW_HAS_SECURE_API=1 -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NEEDS_QMAIN -DQT_NETWORK_LIB -DQT_OPENGL_LIB -DQT_QMLINTEGRATION_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QUICK_LIB -DUNICODE -DWIN32 -DWIN64 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -D_UNICODE -D_WIN64 -IC:/Users/<USER>/Documents/augment-projects/qmltest/build/qmltest_autogen/include -IC:/Users/<USER>/Documents/augment-projects/qmltest -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtQml/6.9.1/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1 -isystem C:/Qt/6.9.1/mingw_64/include/QtCore/6.9.1/QtCore -isystem C:/Qt/6.9.1/mingw_64/include/QtCore -isystem C:/Qt/6.9.1/mingw_64/include -isystem C:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -isystem C:/Qt/6.9.1/mingw_64/include/QtQml -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlIntegration -isystem C:/Qt/6.9.1/mingw_64/include/QtNetwork -isystem C:/Qt/6.9.1/mingw_64/include/QtQuick -isystem C:/Qt/6.9.1/mingw_64/include/QtGui -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlMeta -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlModels -isystem C:/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -isystem C:/Qt/6.9.1/mingw_64/include/QtOpenGL -O0 -g -Xclang -gcodeview -std=gnu++17 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -o CMakeFiles\\qmltest.dir\\build\\.qt\\rcc\\qrc_qmltest_raw_qml_0.cpp.obj -c C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\.qt\\rcc\\qrc_qmltest_raw_qml_0.cpp", "file": "C:\\Users\\<USER>\\Documents\\augment-projects\\qmltest\\build\\.qt\\rcc\\qrc_qmltest_raw_qml_0.cpp", "output": "CMakeFiles\\qmltest.dir\\build\\.qt\\rcc\\qrc_qmltest_raw_qml_0.cpp.obj"}]