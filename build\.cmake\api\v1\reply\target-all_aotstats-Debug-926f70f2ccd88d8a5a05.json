{"backtrace": 3, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_deferred_aotstats_setup"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake:1103:EVAL", "CMakeLists.txt"], "nodes": [{"file": 2}, {"file": 2, "line": -1, "parent": 0}, {"command": 1, "file": 1, "line": 1, "parent": 1}, {"command": 0, "file": 0, "line": 1210, "parent": 2}]}, "dependencies": [{"id": "module_qmltest_aotstats_target::@6890427a1f51a3e7e1df"}], "id": "all_aotstats::@6890427a1f51a3e7e1df", "name": "all_aotstats", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0]}, {"name": "CMake Rules", "sourceIndexes": [1, 2, 3, 4, 5]}], "sources": [{"backtrace": 3, "isGenerated": true, "path": "build/CMakeFiles/all_aotstats", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "build/CMakeFiles/all_aotstats.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/all_aotstats.aotstats.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/module_qmltest.aotstats.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/.rcc/qmlcache/qmltest_main_qml.cpp.rule", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "build/qmltest_qmltyperegistrations.cpp.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}