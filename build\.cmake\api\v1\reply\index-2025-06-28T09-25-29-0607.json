{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/cmake-4.0.2-windows-x86_64/bin/cmake.exe", "cpack": "F:/cmake-4.0.2-windows-x86_64/bin/cpack.exe", "ctest": "F:/cmake-4.0.2-windows-x86_64/bin/ctest.exe", "root": "F:/cmake-4.0.2-windows-x86_64/share/cmake-4.0"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 2, "string": "4.0.2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-392426caaa022102d16e.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-cd18243ee21e5a86fd2b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-e0ae6158a63594cc08f5.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-af75ae057697decfffe6.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-cd18243ee21e5a86fd2b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-392426caaa022102d16e.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "toolchains-v1-af75ae057697decfffe6.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-e0ae6158a63594cc08f5.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}