{"backtrace": 7, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_add_phony_target", "_qt_internal_add_all_qmllint_target", "_qt_internal_target_enable_qmllint", "qt6_target_qml_sources", "qt6_add_qml_module", "qt_add_qml_module", "add_dependencies", "_qt_internal_add_phony_target_dependencies"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "CMakeLists.txt"], "nodes": [{"file": 2}, {"command": 6, "file": 2, "line": 16, "parent": 0}, {"command": 5, "file": 1, "line": 1252, "parent": 1}, {"command": 4, "file": 1, "line": 916, "parent": 2}, {"command": 3, "file": 1, "line": 3228, "parent": 3}, {"command": 2, "file": 1, "line": 1579, "parent": 4}, {"command": 1, "file": 1, "line": 1593, "parent": 5}, {"command": 0, "file": 0, "line": 299, "parent": 6}, {"command": 8, "file": 1, "line": 1597, "parent": 5}, {"command": 7, "file": 0, "line": 328, "parent": 8}]}, "dependencies": [{"backtrace": 9, "id": "qmltest_qmllint_json::@6890427a1f51a3e7e1df"}], "folder": {"name": "QtInternalTargets/QmlLinter"}, "id": "all_qmllint_json::@6890427a1f51a3e7e1df", "name": "all_qmllint_json", "paths": {"build": ".", "source": "."}, "sources": [], "type": "UTILITY"}