{"tasks": [{"type": "cppbuild", "label": "C/C++: cl.exe build active file", "command": "cl.exe", "args": ["/Zi", "/EHsc", "/nologo", "/Fe${fileDirname}\\${fileBasenameNoExtension}.exe", "${file}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$msCompile"], "group": "build", "detail": "Task generated by <PERSON>bugger."}, {"type": "cppbuild", "label": "C/C++: clang.exe build active file", "command": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin\\clang.exe", "args": ["-fcolor-diagnostics", "-fansi-escape-codes", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\Llvm\\x64\\bin"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}], "version": "2.0.0"}