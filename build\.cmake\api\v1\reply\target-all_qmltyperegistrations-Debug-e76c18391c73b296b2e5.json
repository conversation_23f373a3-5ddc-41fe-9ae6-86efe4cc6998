{"backtrace": 4, "backtraceGraph": {"commands": ["add_custom_target", "_qt_internal_qml_type_registration", "qt6_add_qml_module", "qt_add_qml_module", "add_dependencies"], "files": ["C:/Qt/6.9.1/mingw_64/lib/cmake/Qt6Qml/Qt6QmlMacros.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 16, "parent": 0}, {"command": 2, "file": 0, "line": 1252, "parent": 1}, {"command": 1, "file": 0, "line": 796, "parent": 2}, {"command": 0, "file": 0, "line": 3847, "parent": 3}, {"command": 4, "file": 0, "line": 3850, "parent": 3}]}, "dependencies": [{"backtrace": 5, "id": "qmltest_qmltyperegistration::@6890427a1f51a3e7e1df"}], "folder": {"name": "QtInternalTargets"}, "id": "all_qmltyperegistrations::@6890427a1f51a3e7e1df", "name": "all_qmltyperegistrations", "paths": {"build": ".", "source": "."}, "sources": [], "type": "UTILITY"}