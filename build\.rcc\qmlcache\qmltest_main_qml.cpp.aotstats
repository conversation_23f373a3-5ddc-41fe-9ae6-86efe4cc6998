[{"moduleFiles": [{"entries": [{"codegenSuccessful": false, "column": 5, "durationMicroseconds": 2503, "errorMessage": "Cannot generate efficient code for lookup in QJSValue", "functionName": "groupDataByLan", "line": 38}, {"codegenSuccessful": true, "column": 28, "durationMicroseconds": 43, "errorMessage": "", "functionName": "onCompleted", "line": 67}, {"codegenSuccessful": true, "column": 23, "durationMicroseconds": 44, "errorMessage": "", "functionName": "fill", "line": 72}, {"codegenSuccessful": true, "column": 24, "durationMicroseconds": 63, "errorMessage": "", "functionName": "cellWidth", "line": 80}, {"codegenSuccessful": true, "column": 20, "durationMicroseconds": 36, "errorMessage": "", "functionName": "model", "line": 83}, {"codegenSuccessful": true, "column": 27, "durationMicroseconds": 25, "errorMessage": "", "functionName": "fill", "line": 77}, {"codegenSuccessful": false, "column": 24, "durationMicroseconds": 24, "errorMessage": "Cannot access value for name gridView", "functionName": "width", "line": 86}, {"codegenSuccessful": false, "column": 25, "durationMicroseconds": 17, "errorMessage": "Cannot access value for name gridView", "functionName": "height", "line": 87}, {"codegenSuccessful": true, "column": 35, "durationMicroseconds": 28, "errorMessage": "", "functionName": "fill", "line": 94}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 34, "errorMessage": "", "functionName": "width", "line": 100}, {"codegenSuccessful": false, "column": 35, "durationMicroseconds": 19, "errorMessage": "Cannot access value for name model", "functionName": "text", "line": 107}, {"codegenSuccessful": true, "column": 47, "durationMicroseconds": 23, "errorMessage": "", "functionName": "centerIn", "line": 106}, {"codegenSuccessful": true, "column": 32, "durationMicroseconds": 35, "errorMessage": "", "functionName": "width", "line": 116}, {"codegenSuccessful": false, "column": 36, "durationMicroseconds": 114, "errorMessage": "Cannot access value for name devices", "functionName": "model", "line": 120}, {"codegenSuccessful": true, "column": 40, "durationMicroseconds": 34, "errorMessage": "", "functionName": "width", "line": 123}, {"codegenSuccessful": false, "column": 40, "durationMicroseconds": 126, "errorMessage": "Cannot access value for name modelData", "functionName": "color", "line": 125}, {"codegenSuccessful": false, "column": 47, "durationMicroseconds": 102, "errorMessage": "Cannot access value for name modelData", "functionName": "color", "line": 126}, {"codegenSuccessful": false, "column": 43, "durationMicroseconds": 161, "errorMessage": "Cannot access value for name modelData", "functionName": "text", "line": 132}, {"codegenSuccessful": true, "column": 55, "durationMicroseconds": 31, "errorMessage": "", "functionName": "centerIn", "line": 131}, {"codegenSuccessful": false, "column": 48, "durationMicroseconds": 145, "errorMessage": "Cannot access value for name modelData", "functionName": "bold", "line": 135}], "filePath": "C:/Users/<USER>/Documents/augment-projects/qmltest/main.qml"}], "moduleId": "qmltest(qmltest)"}]